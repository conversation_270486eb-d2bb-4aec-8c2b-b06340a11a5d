import { supabase, Category } from '../lib/supabase';

export interface CreateCategoryData {
  name: string;
  description?: string;
  image_url?: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string;
}

export class CategoryService {
  // Get all categories
  static async getCategories() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`);
    }

    return data;
  }

  // Get categories with product counts
  static async getCategoriesWithProductCounts() {
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        products (count)
      `)
      .order('name', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch categories with product counts: ${error.message}`);
    }

    return data;
  }

  // Get a single category by ID
  static async getCategoryById(id: string) {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch category: ${error.message}`);
    }

    return data;
  }

  // Get category with its products
  static async getCategoryWithProducts(id: string) {
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        products (*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch category with products: ${error.message}`);
    }

    return data;
  }

  // Create a new category
  static async createCategory(categoryData: CreateCategoryData) {
    const { data, error } = await supabase
      .from('categories')
      .insert([categoryData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`);
    }

    return data;
  }

  // Update a category
  static async updateCategory(categoryData: UpdateCategoryData) {
    const { id, ...updateData } = categoryData;
    
    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`);
    }

    return data;
  }

  // Delete a category
  static async deleteCategory(id: string) {
    // First check if category has products
    const { data: products, error: checkError } = await supabase
      .from('products')
      .select('id')
      .eq('category_id', id)
      .limit(1);

    if (checkError) {
      throw new Error(`Failed to check category products: ${checkError.message}`);
    }

    if (products && products.length > 0) {
      throw new Error('Cannot delete category that contains products. Please move or delete products first.');
    }

    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete category: ${error.message}`);
    }

    return true;
  }

  // Get category by name
  static async getCategoryByName(name: string) {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('name', name)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Failed to fetch category: ${error.message}`);
    }

    return data;
  }

  // Check if category name exists (for validation)
  static async categoryNameExists(name: string, excludeId?: string) {
    let query = supabase
      .from('categories')
      .select('id')
      .eq('name', name);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.limit(1);

    if (error) {
      throw new Error(`Failed to check category name: ${error.message}`);
    }

    return data && data.length > 0;
  }
}
