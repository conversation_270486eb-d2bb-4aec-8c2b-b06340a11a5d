import { supabase, Customer } from '../lib/supabase';

export interface CreateCustomerData {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {
  id: string;
}

export class CustomerService {
  // Get all customers with optional filtering
  static async getCustomers(filters?: {
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase
      .from('customers')
      .select(`
        *,
        orders (count)
      `);

    if (filters?.search) {
      query = query.or(`
        first_name.ilike.%${filters.search}%,
        last_name.ilike.%${filters.search}%,
        email.ilike.%${filters.search}%
      `);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch customers: ${error.message}`);
    }

    return data;
  }

  // Get a single customer by ID
  static async getCustomerById(id: string) {
    const { data, error } = await supabase
      .from('customers')
      .select(`
        *,
        orders (
          id,
          total_amount,
          status,
          created_at
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch customer: ${error.message}`);
    }

    return data;
  }

  // Get customer by email
  static async getCustomerByEmail(email: string) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Failed to fetch customer: ${error.message}`);
    }

    return data;
  }

  // Create a new customer
  static async createCustomer(customerData: CreateCustomerData) {
    // Check if email already exists
    const existingCustomer = await this.getCustomerByEmail(customerData.email);
    if (existingCustomer) {
      throw new Error('A customer with this email already exists');
    }

    const { data, error } = await supabase
      .from('customers')
      .insert([customerData])
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create customer: ${error.message}`);
    }

    return data;
  }

  // Update a customer
  static async updateCustomer(customerData: UpdateCustomerData) {
    const { id, ...updateData } = customerData;
    
    // If email is being updated, check if it already exists
    if (updateData.email) {
      const existingCustomer = await this.getCustomerByEmail(updateData.email);
      if (existingCustomer && existingCustomer.id !== id) {
        throw new Error('A customer with this email already exists');
      }
    }

    const { data, error } = await supabase
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update customer: ${error.message}`);
    }

    return data;
  }

  // Delete a customer
  static async deleteCustomer(id: string) {
    // Check if customer has orders
    const { data: orders, error: checkError } = await supabase
      .from('orders')
      .select('id')
      .eq('customer_id', id)
      .limit(1);

    if (checkError) {
      throw new Error(`Failed to check customer orders: ${checkError.message}`);
    }

    if (orders && orders.length > 0) {
      throw new Error('Cannot delete customer with existing orders. Please handle orders first.');
    }

    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete customer: ${error.message}`);
    }

    return true;
  }

  // Search customers
  static async searchCustomers(searchTerm: string, limit = 20) {
    return this.getCustomers({ search: searchTerm, limit });
  }

  // Get customer statistics
  static async getCustomerStats() {
    const { data, error } = await supabase
      .from('customers')
      .select(`
        id,
        created_at,
        orders (
          total_amount
        )
      `);

    if (error) {
      throw new Error(`Failed to fetch customer stats: ${error.message}`);
    }

    const stats = {
      total_customers: data.length,
      customers_with_orders: data.filter(customer => customer.orders.length > 0).length,
      total_customer_value: data.reduce((sum, customer) => {
        const customerValue = customer.orders.reduce((orderSum: number, order: any) => 
          orderSum + order.total_amount, 0);
        return sum + customerValue;
      }, 0),
      average_customer_value: 0
    };

    stats.average_customer_value = stats.customers_with_orders > 0 
      ? stats.total_customer_value / stats.customers_with_orders 
      : 0;

    return stats;
  }

  // Get top customers by order value
  static async getTopCustomers(limit = 10) {
    const { data, error } = await supabase
      .from('customers')
      .select(`
        *,
        orders (
          total_amount
        )
      `)
      .limit(limit);

    if (error) {
      throw new Error(`Failed to fetch top customers: ${error.message}`);
    }

    // Calculate total order value for each customer and sort
    const customersWithValue = data.map(customer => ({
      ...customer,
      total_order_value: customer.orders.reduce((sum: number, order: any) => 
        sum + order.total_amount, 0)
    })).sort((a, b) => b.total_order_value - a.total_order_value);

    return customersWithValue.slice(0, limit);
  }

  // Check if email exists (for validation)
  static async emailExists(email: string, excludeId?: string) {
    let query = supabase
      .from('customers')
      .select('id')
      .eq('email', email);

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query.limit(1);

    if (error) {
      throw new Error(`Failed to check email: ${error.message}`);
    }

    return data && data.length > 0;
  }
}
