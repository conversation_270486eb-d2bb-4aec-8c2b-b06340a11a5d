import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Filter, Grid, List, ChevronDown, Star } from 'lucide-react';

const ProductsPage = () => {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const urlCategory = params.get('category');

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');

  // Mock products data
  const products = [
    {
      id: 1,
      name: 'Premium Cotton T-Shirt',
      price: 29.99,
      originalPrice: 39.99,
      rating: 4.5,
      reviews: 128,
      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=500&fit=crop',
      category: 'Fashion',
      isNew: true,
      isSale: true
    },
    {
      id: 2,
      name: 'Wireless Bluetooth Headphones',
      price: 89.99,
      originalPrice: 129.99,
      rating: 4.8,
      reviews: 256,
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=500&fit=crop',
      category: 'Electronics',
      isNew: false,
      isSale: true
    },
    {
      id: 3,
      name: 'Ceramic Coffee Mug Set',
      price: 24.99,
      originalPrice: 24.99,
      rating: 4.3,
      reviews: 89,
      image: 'https://images.unsplash.com/photo-1513558161293-cdaf765ed2fd?w=400&h=500&fit=crop',
      category: 'Home & Living',
      isNew: true,
      isSale: false
    },
    {
      id: 4,
      name: 'Handcrafted Wooden Bowl',
      price: 45.99,
      originalPrice: 45.99,
      rating: 4.7,
      reviews: 67,
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=500&fit=crop',
      category: 'Artisan',
      isNew: false,
      isSale: false
    },
    {
      id: 5,
      name: 'Organic Cotton Hoodie',
      price: 59.99,
      originalPrice: 79.99,
      rating: 4.6,
      reviews: 203,
      image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=500&fit=crop',
      category: 'Fashion',
      isNew: false,
      isSale: true
    },
    {
      id: 6,
      name: 'Smart Fitness Watch',
      price: 199.99,
      originalPrice: 249.99,
      rating: 4.9,
      reviews: 445,
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=500&fit=crop',
      category: 'Electronics',
      isNew: true,
      isSale: true
    },
    {
      id: 7,
      name: 'Minimalist Desk Lamp',
      price: 34.99,
      originalPrice: 34.99,
      rating: 4.4,
      reviews: 156,
      image: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c?w=400&h=500&fit=crop',
      category: 'Home & Living',
      isNew: false,
      isSale: false
    },
    {
      id: 8,
      name: 'Handmade Leather Wallet',
      price: 79.99,
      originalPrice: 79.99,
      rating: 4.8,
      reviews: 98,
      image: 'https://images.unsplash.com/photo-1627123424574-724758594e93?w=400&h=500&fit=crop',
      category: 'Artisan',
      isNew: true,
      isSale: false
    }
  ];

  const categories = ['All', 'Fashion', 'Electronics', 'Home & Living', 'Artisan'];
  const [selectedCategory, setSelectedCategory] = useState(urlCategory ? urlCategory.replace(/%20/g, ' ') : 'All');

  React.useEffect(() => {
    if (urlCategory) {
      setSelectedCategory(urlCategory.replace(/%20/g, ' '));
    }
  }, [urlCategory]);

  const filteredProducts = selectedCategory === 'All' 
    ? products 
    : products.filter(product => product.category === selectedCategory);

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">All Products</h1>
        <p className="text-gray-600">Discover our curated collection of premium products</p>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 space-y-4 lg:space-y-0">
        {/* Category Filters */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category
                  ? 'bg-black text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Sort and View Controls */}
        <div className="flex items-center space-x-4">
          {/* Sort Dropdown */}
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            >
              <option value="featured">Featured</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" />
          </div>

          {/* View Mode Toggle */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 transition-colors ${
                viewMode === 'grid' ? 'bg-black text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 transition-colors ${
                viewMode === 'list' ? 'bg-black text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className={`grid gap-6 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
          : 'grid-cols-1'
      }`}>
        {filteredProducts.map((product) => (
          <div key={product.id} className={`bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow ${
            viewMode === 'list' ? 'flex' : ''
          }`}>
            <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>
              <img
                src={product.image}
                alt={product.name}
                className={`w-full object-cover ${viewMode === 'list' ? 'h-48' : 'h-64'}`}
              />
              {product.isNew && (
                <span className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                  New
                </span>
              )}
              {product.isSale && (
                <span className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  Sale
                </span>
              )}
            </div>
            
            <div className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
              <div className="mb-2">
                <span className="text-xs text-gray-500 uppercase tracking-wide">{product.category}</span>
              </div>
              
              <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
              
              <div className="flex items-center mb-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500 ml-2">({product.reviews})</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="font-bold text-lg text-gray-900">${product.price}</span>
                  {product.originalPrice !== product.price && (
                    <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                  )}
                </div>
                
                <button className="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors">
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-12">
        <nav className="flex items-center space-x-2">
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700 disabled:opacity-50">
            Previous
          </button>
          <button className="px-3 py-2 bg-black text-white rounded-lg">1</button>
          <button className="px-3 py-2 text-gray-700 hover:text-black">2</button>
          <button className="px-3 py-2 text-gray-700 hover:text-black">3</button>
          <button className="px-3 py-2 text-gray-500 hover:text-gray-700">
            Next
          </button>
        </nav>
      </div>
    </div>
  );
};

export default ProductsPage; 