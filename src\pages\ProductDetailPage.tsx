import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Star, Heart, Share2, Truck, Shield, RotateCcw, ChevronLeft, ChevronRight, ShoppingBag } from 'lucide-react';

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState('M');

  // Mock product data (in a real app, fetch by id)
  const product = {
    id: 1,
    name: 'Premium Organic Cotton T-Shirt',
    price: 29.99,
    originalPrice: 39.99,
    rating: 4.5,
    reviews: 128,
    description: 'Crafted from 100% organic cotton, this premium t-shirt offers exceptional comfort and durability. The soft, breathable fabric makes it perfect for everyday wear, while the classic fit ensures a flattering silhouette.',
    features: [
      '100% Organic Cotton',
      'Breathable and soft',
      'Classic fit',
      'Machine washable',
      'Sustainably sourced'
    ],
    images: [
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=600&h=700&fit=crop',
      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=600&h=700&fit=crop',
      'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=600&h=700&fit=crop',
      'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=600&h=700&fit=crop'
    ],
    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    colors: ['Black', 'White', 'Navy', 'Gray'],
    inStock: true,
    category: 'Fashion',
    tags: ['Organic', 'Cotton', 'Premium', 'Sustainable']
  };

  const relatedProducts = [
    {
      id: 2,
      name: 'Organic Cotton Hoodie',
      price: 59.99,
      originalPrice: 79.99,
      image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=300&h=400&fit=crop',
      category: 'Fashion',
      rating: 4.6,
      reviews: 203,
      badge: 'Best Seller',
      isNew: false,
      isSale: true
    },
    {
      id: 3,
      name: 'Premium Denim Jeans',
      price: 89.99,
      originalPrice: 89.99,
      image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=300&h=400&fit=crop',
      category: 'Fashion',
      rating: 4.8,
      reviews: 156,
      badge: 'Premium',
      isNew: true,
      isSale: false
    },
    {
      id: 4,
      name: 'Casual Linen Shirt',
      price: 44.99,
      originalPrice: 54.99,
      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=300&h=400&fit=crop',
      category: 'Fashion',
      rating: 4.4,
      reviews: 89,
      badge: 'Eco-Friendly',
      isNew: false,
      isSale: true
    },
    {
      id: 5,
      name: 'Wireless Bluetooth Headphones',
      price: 89.99,
      originalPrice: 129.99,
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=400&fit=crop',
      category: 'Electronics',
      rating: 4.8,
      reviews: 256,
      badge: 'Top Rated',
      isNew: false,
      isSale: true
    },
    {
      id: 6,
      name: 'Smart Fitness Watch',
      price: 199.99,
      originalPrice: 249.99,
      image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=400&fit=crop',
      category: 'Electronics',
      rating: 4.9,
      reviews: 445,
      badge: 'New',
      isNew: true,
      isSale: true
    },
    {
      id: 7,
      name: 'Ceramic Coffee Mug Set',
      price: 24.99,
      originalPrice: 24.99,
      image: 'https://images.unsplash.com/photo-1513558161293-cdaf765ed2fd?w=300&h=400&fit=crop',
      category: 'Home & Living',
      rating: 4.3,
      reviews: 89,
      badge: 'Handmade',
      isNew: true,
      isSale: false
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="text-sm text-gray-500 mb-8">
        <ol className="flex items-center space-x-2">
          <li><a href="/" className="hover:text-black">Home</a></li>
          <li>/</li>
          <li><a href="/products" className="hover:text-black">Products</a></li>
          <li>/</li>
          <li className="text-black">{product.name}</li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <div>
          <div className="relative mb-4">
            <img
              src={product.images[selectedImage]}
              alt={product.name}
              className="w-full h-96 object-cover rounded-lg"
            />
            <button className="absolute top-4 right-4 p-2 bg-white rounded-full shadow-md hover:bg-gray-50">
              <Heart className="w-5 h-5" />
            </button>
            <button className="absolute top-4 left-4 p-2 bg-white rounded-full shadow-md hover:bg-gray-50">
              <Share2 className="w-5 h-5" />
            </button>
          </div>
          
          {/* Thumbnail Images */}
          <div className="grid grid-cols-4 gap-2">
            {product.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`border-2 rounded-lg overflow-hidden ${
                  selectedImage === index ? 'border-black' : 'border-gray-200'
                }`}
              >
                <img
                  src={image}
                  alt={`${product.name} ${index + 1}`}
                  className="w-full h-20 object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div>
          <div className="mb-4">
            <span className="text-sm text-gray-500 uppercase tracking-wide">{product.category}</span>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>
          
          {/* Rating */}
          <div className="flex items-center mb-4">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-5 h-5 ${
                    i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-500 ml-2">{product.rating} ({product.reviews} reviews)</span>
          </div>
          
          {/* Price */}
          <div className="flex items-center mb-6">
            <span className="text-3xl font-bold text-gray-900">${product.price}</span>
            {product.originalPrice !== product.price && (
              <span className="text-lg text-gray-500 line-through ml-2">${product.originalPrice}</span>
            )}
            {product.originalPrice !== product.price && (
              <span className="bg-red-100 text-red-600 text-sm font-medium px-2 py-1 rounded ml-2">
                {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
              </span>
            )}
          </div>
          
          {/* Description */}
          <p className="text-gray-600 mb-6">{product.description}</p>
          
          {/* Features */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-2">Features:</h3>
            <ul className="space-y-1">
              {product.features.map((feature, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-center">
                  <span className="w-1.5 h-1.5 bg-black rounded-full mr-2"></span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
          
          {/* Size Selection */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-2">Size:</h3>
            <div className="flex flex-wrap gap-2">
              {product.sizes.map((size) => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
                    selectedSize === size
                      ? 'border-black bg-black text-white'
                      : 'border-gray-300 text-gray-700 hover:border-black'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>
          
          {/* Quantity */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-2">Quantity:</h3>
            <div className="flex items-center border border-gray-300 rounded-lg w-32">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="px-3 py-2 hover:bg-gray-50"
              >
                -
              </button>
              <span className="flex-1 text-center py-2">{quantity}</span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="px-3 py-2 hover:bg-gray-50"
              >
                +
              </button>
            </div>
          </div>
          
          {/* Add to Cart */}
          <div className="flex space-x-4 mb-6">
            <button className="flex-1 bg-black text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors">
              Add to Cart
            </button>
            <button 
              onClick={() => navigate('/checkout', { 
                state: { 
                  singleProduct: {
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    image: product.images[0],
                    quantity: quantity
                  }
                } 
              })}
              className="flex-1 border border-black text-black py-3 px-6 rounded-lg font-medium hover:bg-black hover:text-white transition-colors"
            >
              Buy Now
            </button>
          </div>
          
          {/* Shipping Info */}
          <div className="border-t pt-6">
            <div className="flex items-center mb-2">
              <Truck className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">Free shipping on orders over $100</span>
            </div>
            <div className="flex items-center mb-2">
              <Shield className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">30-day return policy</span>
            </div>
            <div className="flex items-center">
              <RotateCcw className="w-5 h-5 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600">Easy exchanges</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Related Products */}
      <div className="mt-20 bg-gray-50 py-16 -mx-4 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">You might also like</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover more products that complement your style and preferences
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {relatedProducts.map((relatedProduct) => (
              <div
                key={relatedProduct.id}
                className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer group border border-gray-100"
                onClick={() => navigate(`/products/${relatedProduct.id}`)}
              >
                <div className="relative">
                  <img
                    src={relatedProduct.image}
                    alt={relatedProduct.name}
                    className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Badges */}
                  <div className="absolute top-3 left-3 flex flex-col gap-2">
                    {relatedProduct.isNew && (
                      <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        New
                      </span>
                    )}
                    {relatedProduct.isSale && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        Sale
                      </span>
                    )}
                  </div>
                  
                  {/* Category Badge */}
                  <span className="absolute top-3 right-3 bg-black/80 text-white text-xs px-2 py-1 rounded-full">
                    {relatedProduct.category}
                  </span>
                  
                  {/* Quick Actions */}
                  <div className="absolute bottom-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <button 
                      className="bg-white p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                      onClick={e => {e.stopPropagation(); /* Add to wishlist */}}
                    >
                      <Heart className="w-4 h-4" />
                    </button>
                    <button 
                      className="bg-black text-white p-2 rounded-full shadow-lg hover:bg-gray-800 transition-colors"
                      onClick={e => {e.stopPropagation(); /* Add to cart */}}
                    >
                      <ShoppingBag className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-500 uppercase tracking-wide">{relatedProduct.category}</span>
                    <div className="flex items-center">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600 ml-1">{relatedProduct.rating}</span>
                    </div>
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-black transition-colors line-clamp-2">
                    {relatedProduct.name}
                  </h3>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="font-bold text-lg text-gray-900">${relatedProduct.price}</span>
                      {relatedProduct.originalPrice !== relatedProduct.price && (
                        <span className="text-sm text-gray-500 line-through">${relatedProduct.originalPrice}</span>
                      )}
                    </div>
                    {relatedProduct.originalPrice !== relatedProduct.price && (
                      <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                        -{Math.round(((relatedProduct.originalPrice - relatedProduct.price) / relatedProduct.originalPrice) * 100)}%
                      </span>
                    )}
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      className="flex-1 bg-black text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors"
                      onClick={e => {e.stopPropagation(); /* Add to cart logic */}}
                    >
                      Add to Cart
                    </button>
                    <button
                      className="flex-1 bg-gray-100 text-black py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                      onClick={e => {e.stopPropagation(); navigate(`/products/${relatedProduct.id}`);}}
                    >
                      View
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <button 
              className="bg-black text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
              onClick={() => navigate('/products')}
            >
              View All Products
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage; 