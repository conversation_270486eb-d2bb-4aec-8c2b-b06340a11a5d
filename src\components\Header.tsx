import React, { useState } from 'react';
import { Search, ShoppingBag, Menu, X } from 'lucide-react';
import { Link } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      {/* Top bar */}
      <div className="bg-black text-white text-sm py-2">
        <div className="max-w-7xl mx-auto px-4 text-center">
          Free shipping on orders over $100 | 30-day returns
        </div>
      </div>

      {/* Main header */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="text-2xl font-bold text-gray-900">LUXE</Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link to="/products?category=Fashion" className="text-gray-700 hover:text-black transition-colors font-medium">Fashion</Link>
            <Link to="/products?category=Electronics" className="text-gray-700 hover:text-black transition-colors font-medium">Electronics</Link>
            <Link to="/products?category=Home%20%26%20Living" className="text-gray-700 hover:text-black transition-colors font-medium">Home & Living</Link>
            <Link to="/products?category=Artisan" className="text-gray-700 hover:text-black transition-colors font-medium">Artisan</Link>
            <Link to="/products?category=Sale" className="text-gray-700 hover:text-black transition-colors font-medium">Sale</Link>
          </nav>

          {/* Search and Icons */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="hidden md:flex items-center bg-gray-100 rounded-full px-4 py-2 w-64">
              <Search className="w-4 h-4 text-gray-500 mr-2" />
              <input 
                type="text" 
                placeholder="Search products..." 
                className="bg-transparent outline-none flex-1 text-sm"
              />
            </div>

            {/* Cart Icon */}
            <Link to="/cart" className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <ShoppingBag className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
            </Link>

            {/* Mobile menu button */}
            <button 
              className="md:hidden p-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t pt-4">
            <div className="flex flex-col space-y-3">
              <Link to="/products?category=Fashion" className="text-gray-700 hover:text-black transition-colors font-medium">Fashion</Link>
              <Link to="/products?category=Electronics" className="text-gray-700 hover:text-black transition-colors font-medium">Electronics</Link>
              <Link to="/products?category=Home%20%26%20Living" className="text-gray-700 hover:text-black transition-colors font-medium">Home & Living</Link>
              <Link to="/products?category=Artisan" className="text-gray-700 hover:text-black transition-colors font-medium">Artisan</Link>
              <Link to="/products?category=Sale" className="text-gray-700 hover:text-black transition-colors font-medium">Sale</Link>
            </div>
            <div className="mt-4 flex items-center bg-gray-100 rounded-full px-4 py-2">
              <Search className="w-4 h-4 text-gray-500 mr-2" />
              <input 
                type="text" 
                placeholder="Search products..." 
                className="bg-transparent outline-none flex-1 text-sm"
              />
            </div>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;