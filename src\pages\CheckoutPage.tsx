import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

interface CartItem {
  id: number;
  name: string;
  price: number;
  image: string;
  quantity: number;
  size: string;
  color: string;
}

interface CheckoutData {
  cartItems?: CartItem[];
  total?: number;
  singleProduct?: {
    id: number;
    name: string;
    price: number;
    image: string;
    quantity: number;
  };
}

const CheckoutPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const checkoutData: CheckoutData = location.state || {};

  const [formData, setFormData] = useState({
    cardNumber: '',
    expirationDate: '',
    securityCode: '',
    nameOnCard: ''
  });

  // Calculate totals based on checkout data
  const getOrderItems = () => {
    if (checkoutData.singleProduct) {
      return [{
        id: checkoutData.singleProduct.id,
        name: checkoutData.singleProduct.name,
        price: checkoutData.singleProduct.price,
        quantity: checkoutData.singleProduct.quantity,
        icon: 'fa-briefcase',
        iconColor: 'text-indigo-500',
        bgColor: 'bg-indigo-100',
        description: 'Single item purchase'
      }];
    } else if (checkoutData.cartItems) {
      return checkoutData.cartItems.map(item => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        icon: 'fa-shopping-bag',
        iconColor: 'text-blue-500',
        bgColor: 'bg-blue-100',
        description: `${item.color} • ${item.size}`
      }));
    }
    return [];
  };

  const orderItems = getOrderItems();
  const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + tax;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically process the payment
    alert('Payment processed successfully!');
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-100 font-['Plus_Jakarta_Sans']">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-4xl w-full bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* Header with back button */}
          <div className="p-6 border-b border-gray-200">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-black transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to {checkoutData.singleProduct ? 'Product' : 'Cart'}
            </button>
          </div>

          <div className="md:flex">
            {/* Left Column: Payment Form */}
            <div className="md:w-3/5 p-8">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800">Complete your payment</h2>
                <p className="text-gray-500 mt-2">Enter your card details to process your payment</p>
              </div>
              
              {/* Payment Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Card number</label>
                  <div className="relative">
                    <input
                      type="text"
                      name="cardNumber"
                      value={formData.cardNumber}
                      onChange={handleInputChange}
                      placeholder="1234 5678 9012 3456"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                      required
                    />
                    <div className="absolute right-3 top-3 flex space-x-2">
                      <i className="fa-brands fa-cc-visa text-blue-700 text-xl"></i>
                      <i className="fa-brands fa-cc-mastercard text-xl"></i>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Expiration date</label>
                    <input
                      type="text"
                      name="expirationDate"
                      value={formData.expirationDate}
                      onChange={handleInputChange}
                      placeholder="MM / YY"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Security code</label>
                    <div className="relative">
                      <input
                        type="text"
                        name="securityCode"
                        value={formData.securityCode}
                        onChange={handleInputChange}
                        placeholder="CVC"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                        required
                      />
                      <div className="absolute right-3 top-3 text-gray-400">
                        <i className="fa-solid fa-circle-question"></i>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name on card</label>
                  <input
                    type="text"
                    name="nameOnCard"
                    value={formData.nameOnCard}
                    onChange={handleInputChange}
                    placeholder="Your name"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                    required
                  />
                </div>
                
                <div className="pt-4">
                  <button
                    type="submit"
                    className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition font-medium flex items-center justify-center"
                  >
                    <span>Pay ${total.toFixed(2)}</span>
                    <i className="fa-solid fa-lock ml-2 text-sm"></i>
                  </button>
                  <p className="text-xs text-center text-gray-500 mt-3 flex items-center justify-center">
                    <i className="fa-solid fa-shield-halved mr-1 text-gray-400"></i>
                    Your payment information is encrypted and secure
                  </p>
                </div>
              </form>
            </div>
            
            {/* Right Column: Order Summary */}
            <div className="md:w-2/5 bg-gray-50 p-8 border-l border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-6">Order summary</h3>
              
              <div className="space-y-4 mb-6">
                {orderItems.map((item) => (
                  <div key={item.id} className="flex justify-between items-start">
                    <div className="flex items-center">
                      <div className={`h-12 w-12 rounded-md ${item.bgColor} flex items-center justify-center`}>
                        <i className={`${item.icon} ${item.iconColor}`}></i>
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-800">{item.name}</p>
                        <p className="text-xs text-gray-500">{item.description}</p>
                      </div>
                    </div>
                    <p className="text-sm font-medium">${(item.price * item.quantity).toFixed(2)}</p>
                  </div>
                ))}
              </div>
              
              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="flex justify-between mb-2">
                  <p className="text-sm text-gray-600">Subtotal</p>
                  <p className="text-sm font-medium text-gray-800">${subtotal.toFixed(2)}</p>
                </div>
                <div className="flex justify-between">
                  <p className="text-sm text-gray-600">Tax</p>
                  <p className="text-sm font-medium text-gray-800">${tax.toFixed(2)}</p>
                </div>
              </div>
              
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-between">
                  <p className="text-base font-medium text-gray-800">Total</p>
                  <p className="text-base font-bold text-gray-800">${total.toFixed(2)}</p>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  By completing this purchase you agree to our <a href="#" className="text-indigo-600 hover:text-indigo-500">terms and conditions</a>
                </p>
              </div>
              
              <div className="mt-8">
                <div className="flex items-center justify-center space-x-3">
                  <i className="fa-brands fa-cc-visa text-blue-700 text-2xl"></i>
                  <i className="fa-brands fa-cc-mastercard text-2xl"></i>
                  <i className="fa-brands fa-cc-amex text-blue-500 text-2xl"></i>
                  <i className="fa-brands fa-cc-paypal text-blue-800 text-2xl"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage; 