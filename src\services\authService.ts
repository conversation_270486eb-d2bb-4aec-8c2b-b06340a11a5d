import { supabase } from '../lib/supabase';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'super_admin';
  created_at: string;
  updated_at: string;
}

export class AuthService {
  // Admin login
  static async loginAdmin(credentials: LoginCredentials) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password,
    });

    if (error) {
      throw new Error(`Login failed: ${error.message}`);
    }

    // Check if user is an admin
    const adminUser = await this.getAdminUser(data.user.email!);
    if (!adminUser) {
      await supabase.auth.signOut();
      throw new Error('Access denied. Admin privileges required.');
    }

    return {
      user: data.user,
      session: data.session,
      adminUser
    };
  }

  // Admin logout
  static async logoutAdmin() {
    const { error } = await supabase.auth.signOut();
    if (error) {
      throw new Error(`Logout failed: ${error.message}`);
    }
    return true;
  }

  // Get current session
  static async getCurrentSession() {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      throw new Error(`Failed to get session: ${error.message}`);
    }

    if (!session) {
      return null;
    }

    // Check if user is still an admin
    const adminUser = await this.getAdminUser(session.user.email!);
    if (!adminUser) {
      await supabase.auth.signOut();
      return null;
    }

    return {
      session,
      adminUser
    };
  }

  // Get admin user by email
  static async getAdminUser(email: string): Promise<AdminUser | null> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Failed to fetch admin user: ${error.message}`);
    }

    return data;
  }

  // Create admin user (for super admins only)
  static async createAdminUser(email: string, password: string, role: 'admin' | 'super_admin' = 'admin') {
    // First create the auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });

    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }

    // Then add to admin_users table
    const { data: adminData, error: adminError } = await supabase
      .from('admin_users')
      .insert([{ email, role }])
      .select()
      .single();

    if (adminError) {
      // Rollback: delete the auth user if admin user creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      throw new Error(`Failed to create admin user: ${adminError.message}`);
    }

    return adminData;
  }

  // Update admin user role
  static async updateAdminUserRole(id: string, role: 'admin' | 'super_admin') {
    const { data, error } = await supabase
      .from('admin_users')
      .update({ role })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update admin user role: ${error.message}`);
    }

    return data;
  }

  // Delete admin user
  static async deleteAdminUser(id: string) {
    // Get the admin user first to get the email
    const { data: adminUser, error: fetchError } = await supabase
      .from('admin_users')
      .select('email')
      .eq('id', id)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch admin user: ${fetchError.message}`);
    }

    // Get the auth user by email
    const { data: authUsers, error: authFetchError } = await supabase.auth.admin.listUsers();
    
    if (authFetchError) {
      throw new Error(`Failed to fetch auth users: ${authFetchError.message}`);
    }

    const authUser = authUsers.users.find(user => user.email === adminUser.email);

    // Delete from admin_users table
    const { error: adminDeleteError } = await supabase
      .from('admin_users')
      .delete()
      .eq('id', id);

    if (adminDeleteError) {
      throw new Error(`Failed to delete admin user: ${adminDeleteError.message}`);
    }

    // Delete from auth if found
    if (authUser) {
      const { error: authDeleteError } = await supabase.auth.admin.deleteUser(authUser.id);
      if (authDeleteError) {
        console.warn(`Failed to delete auth user: ${authDeleteError.message}`);
      }
    }

    return true;
  }

  // Get all admin users
  static async getAdminUsers() {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch admin users: ${error.message}`);
    }

    return data;
  }

  // Check if user has permission for action
  static hasPermission(adminUser: AdminUser, action: string): boolean {
    // Super admins can do everything
    if (adminUser.role === 'super_admin') {
      return true;
    }

    // Regular admins have limited permissions
    const adminPermissions = [
      'view_dashboard',
      'view_products',
      'create_product',
      'update_product',
      'view_categories',
      'view_orders',
      'update_order',
      'view_customers',
      'update_customer'
    ];

    const superAdminOnlyPermissions = [
      'delete_product',
      'create_category',
      'update_category',
      'delete_category',
      'delete_order',
      'delete_customer',
      'manage_admin_users',
      'view_settings',
      'update_settings'
    ];

    return adminPermissions.includes(action);
  }

  // Listen to auth state changes
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}
