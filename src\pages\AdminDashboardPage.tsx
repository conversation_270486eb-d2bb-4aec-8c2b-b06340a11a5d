import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  BarChart3,
  Users,
  Package,
  DollarSign,
  Settings,
  LogOut,
  Menu,
  X,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Eye,
  Star,
  Calendar,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  CreditCard,
  Truck,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import AdminLayout from '../components/AdminLayout';
import { DashboardService, OrderService, AuthService } from '../services';
import type { DashboardStats, RecentActivity, TopProduct } from '../services';

const AdminDashboardPage: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    checkAuthAndLoadData();
  }, [navigate]);

  const checkAuthAndLoadData = async () => {
    try {
      // Check authentication
      const session = await AuthService.getCurrentSession();
      if (!session) {
        navigate('/admin/login');
        return;
      }

      // Load dashboard data
      await loadDashboardData();
    } catch (error) {
      console.error('Authentication or data loading error:', error);
      navigate('/admin/login');
    }
  };

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [stats, orders, activities, products] = await Promise.all([
        DashboardService.getDashboardStats(),
        OrderService.getRecentOrders(5),
        DashboardService.getRecentActivity(4),
        DashboardService.getTopProducts(4)
      ]);

      setDashboardStats(stats);
      setRecentOrders(orders);
      setRecentActivities(activities);
      setTopProducts(products);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await AuthService.logoutAdmin();
      navigate('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if there's an error
      navigate('/admin/login');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatGrowth = (growth: number) => {
    const sign = growth >= 0 ? '+' : '';
    return `${sign}${growth.toFixed(1)}%`;
  };

  const getStatsData = () => {
    if (!dashboardStats) return [];

    return [
      {
        title: 'Total Revenue',
        value: formatCurrency(dashboardStats.totalRevenue),
        change: formatGrowth(dashboardStats.revenueGrowth),
        changeType: dashboardStats.revenueGrowth >= 0 ? 'positive' : 'negative',
        icon: DollarSign,
        color: 'bg-green-500',
        detail: 'vs last month'
      },
      {
        title: 'Total Orders',
        value: dashboardStats.totalOrders.toLocaleString(),
        change: formatGrowth(dashboardStats.ordersGrowth),
        changeType: dashboardStats.ordersGrowth >= 0 ? 'positive' : 'negative',
        icon: ShoppingCart,
        color: 'bg-blue-500',
        detail: 'vs last month'
      },
      {
        title: 'Total Customers',
        value: dashboardStats.totalCustomers.toLocaleString(),
        change: formatGrowth(dashboardStats.customersGrowth),
        changeType: dashboardStats.customersGrowth >= 0 ? 'positive' : 'negative',
        icon: Users,
        color: 'bg-purple-500',
        detail: 'vs last month'
      },
      {
        title: 'Total Products',
        value: dashboardStats.totalProducts.toLocaleString(),
        change: dashboardStats.lowStockProducts > 0 ? `${dashboardStats.lowStockProducts} low stock` : 'In stock',
        changeType: dashboardStats.lowStockProducts > 0 ? 'negative' : 'positive',
        icon: Package,
        color: 'bg-orange-500',
        detail: 'stock status'
      }
    ];
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return { icon: ShoppingCart, color: 'text-blue-500' };
      case 'customer':
        return { icon: Users, color: 'text-green-500' };
      case 'product':
        return { icon: Package, color: 'text-orange-500' };
      default:
        return { icon: Activity, color: 'text-gray-500' };
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const navigation = [
    { name: 'Dashboard', icon: BarChart3, href: '/admin' },
    { name: 'Products', icon: Package, href: '/admin/products' },
    { name: 'Orders', icon: ShoppingCart, href: '/admin/orders' },
    { name: 'Customers', icon: Users, href: '/admin/customers' },
    { name: 'Settings', icon: Settings, href: '/admin/settings' },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-xl sm:text-2xl font-bold">Welcome back, Admin!</h1>
            <p className="text-blue-100 mt-1 text-sm sm:text-base">Here's what's happening with your store today.</p>
          </div>
          <div className="text-left sm:text-right">
            <p className="text-blue-100 text-sm">Today's Date</p>
            <p className="text-lg sm:text-xl font-semibold">{new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                onClick={loadDashboardData}
                className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {getStatsData().map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-4 sm:p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div className="flex items-center min-w-0 flex-1">
                <div className={`p-2 sm:p-3 rounded-full ${stat.color} text-white flex-shrink-0`}>
                  <stat.icon className="h-4 w-4 sm:h-6 sm:w-6" />
                </div>
                <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                  <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">{stat.title}</p>
                  <p className="text-lg sm:text-2xl font-semibold text-gray-900 truncate">{stat.value}</p>
                </div>
              </div>
              <div className="text-right flex-shrink-0 ml-2">
                <div className={`flex items-center ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.changeType === 'positive' ? (
                    <ArrowUpRight className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  )}
                  <span className="text-xs sm:text-sm font-medium">{stat.change}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1 hidden sm:block">{stat.detail}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Analytics Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Sales Chart */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2 sm:mb-0">Sales Overview</h3>
            <div className="flex space-x-2">
              <button className="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-blue-100 text-blue-700 rounded-md">7 Days</button>
              <button className="px-2 sm:px-3 py-1 text-xs sm:text-sm text-gray-500 hover:bg-gray-100 rounded-md">30 Days</button>
              <button className="px-2 sm:px-3 py-1 text-xs sm:text-sm text-gray-500 hover:bg-gray-100 rounded-md">90 Days</button>
            </div>
          </div>
          <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 text-sm sm:text-base">Sales chart will be implemented here</p>
              <p className="text-xs sm:text-sm text-gray-400">Integration with chart library</p>
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3 sm:space-y-4">
            {recentActivities.length > 0 ? (
              recentActivities.map((activity) => {
                const { icon: ActivityIcon, color } = getActivityIcon(activity.type);
                return (
                  <div key={activity.id} className="flex items-start space-x-2 sm:space-x-3">
                    <div className={`p-1.5 sm:p-2 rounded-full bg-gray-100 ${color} flex-shrink-0`}>
                      <ActivityIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm text-gray-900 leading-relaxed break-words">{activity.description}</p>
                      <p className="text-xs text-gray-500 mt-1">{formatDate(activity.timestamp)}</p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-4">
                <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No recent activities</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Orders and Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Recent Orders</h3>
            <button className="text-xs sm:text-sm text-blue-600 hover:text-blue-800">View all</button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order
                  </th>
                  <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.length > 0 ? (
                  recentOrders.slice(0, 4).map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-3 sm:px-6 py-3 sm:py-4">
                        <div className="min-w-0">
                          <div className="text-xs sm:text-sm font-medium text-gray-900 truncate">#{order.id.slice(0, 8)}</div>
                          <div className="text-xs sm:text-sm text-gray-500">{order.order_items?.length || 0} items</div>
                        </div>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4">
                        <div className="min-w-0">
                          <div className="text-xs sm:text-sm font-medium text-gray-900 truncate">
                            {order.customers?.first_name} {order.customers?.last_name}
                          </div>
                          <div className="text-xs sm:text-sm text-gray-500 truncate">{order.customers?.email}</div>
                        </div>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4">
                        <div className="text-xs sm:text-sm text-gray-900 font-medium">{formatCurrency(order.total_amount)}</div>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="px-6 py-8 text-center">
                      <ShoppingCart className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No recent orders</p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Top Products</h3>
            <button className="text-xs sm:text-sm text-blue-600 hover:text-blue-800">View all</button>
          </div>
          <div className="p-4 sm:p-6">
            <div className="space-y-3 sm:space-y-4">
              {topProducts.length > 0 ? (
                topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center space-x-3 sm:space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg overflow-hidden">
                        {product.image_url ? (
                          <img
                            src={product.image_url}
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <Package className="h-4 w-4" />
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm font-medium text-gray-900 truncate">{product.name}</p>
                      <p className="text-xs sm:text-sm text-gray-500">{product.total_sold} sold</p>
                    </div>
                    <div className="text-right flex-shrink-0">
                      <p className="text-xs sm:text-sm font-medium text-gray-900">{formatCurrency(product.revenue)}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <Package className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No product data available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'products':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Products Management</h3>
            <p className="text-gray-600">Product management interface will be implemented here.</p>
          </div>
        );
      case 'orders':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Orders Management</h3>
            <p className="text-gray-600">Order management interface will be implemented here.</p>
          </div>
        );
      case 'customers':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Management</h3>
            <p className="text-gray-600">Customer management interface will be implemented here.</p>
          </div>
        );
      case 'settings':
        return (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Settings</h3>
            <p className="text-gray-600">Settings interface will be implemented here.</p>
          </div>
        );
      default:
        return renderDashboard();
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          {/* Loading skeleton for welcome section */}
          <div className="bg-gray-200 rounded-lg h-32 animate-pulse"></div>

          {/* Loading skeleton for stats cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="animate-pulse">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="ml-4 flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Loading skeleton for charts */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
              <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                    <div className="flex-1">
                      <div className="h-3 bg-gray-200 rounded w-3/4 mb-1 animate-pulse"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {renderContent()}
    </AdminLayout>
  );
};

export default AdminDashboardPage; 