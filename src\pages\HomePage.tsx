import React from 'react';
import { Link } from 'react-router-dom';
import Hero from '../components/Hero';
import CategoryGrid from '../components/CategoryGrid';
import FeaturedProducts from '../components/FeaturedProducts';
import TrustBadges from '../components/TrustBadges';
import Newsletter from '../components/Newsletter';

const HomePage = () => {
  return (
    <div>
      <Hero />
      <div className="flex justify-center my-8">
        <Link to="/products" className="bg-black text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors text-lg">Shop All Products</Link>
      </div>
      <CategoryGrid />
      <FeaturedProducts />
      <TrustBadges />
      <Newsletter />
    </div>
  );
};

export default HomePage; 