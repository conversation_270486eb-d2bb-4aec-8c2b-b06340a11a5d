import { supabase, Order, OrderItem } from '../lib/supabase';

export interface CreateOrderData {
  customer_id: string;
  total_amount: number;
  shipping_address: string;
  billing_address: string;
  items: {
    product_id: string;
    quantity: number;
    price: number;
  }[];
}

export interface UpdateOrderData {
  id: string;
  status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shipping_address?: string;
  billing_address?: string;
}

export class OrderService {
  // Get all orders with optional filtering
  static async getOrders(filters?: {
    status?: string;
    customer_id?: string;
    limit?: number;
    offset?: number;
  }) {
    let query = supabase
      .from('orders')
      .select(`
        *,
        customers (
          id,
          email,
          first_name,
          last_name
        ),
        order_items (
          id,
          quantity,
          price,
          products (
            id,
            name,
            image_url
          )
        )
      `);

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.customer_id) {
      query = query.eq('customer_id', filters.customer_id);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch orders: ${error.message}`);
    }

    return data;
  }

  // Get a single order by ID
  static async getOrderById(id: string) {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customers (
          id,
          email,
          first_name,
          last_name,
          phone,
          address,
          city,
          postal_code,
          country
        ),
        order_items (
          id,
          quantity,
          price,
          products (
            id,
            name,
            description,
            image_url
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(`Failed to fetch order: ${error.message}`);
    }

    return data;
  }

  // Create a new order
  static async createOrder(orderData: CreateOrderData) {
    const { items, ...orderInfo } = orderData;

    // Start a transaction
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([orderInfo])
      .select()
      .single();

    if (orderError) {
      throw new Error(`Failed to create order: ${orderError.message}`);
    }

    // Insert order items
    const orderItems = items.map(item => ({
      ...item,
      order_id: order.id
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      // Rollback: delete the order if items insertion fails
      await supabase.from('orders').delete().eq('id', order.id);
      throw new Error(`Failed to create order items: ${itemsError.message}`);
    }

    // Update product stock quantities
    for (const item of items) {
      const { error: stockError } = await supabase.rpc('update_product_stock', {
        product_id: item.product_id,
        quantity_sold: item.quantity
      });

      if (stockError) {
        console.warn(`Failed to update stock for product ${item.product_id}:`, stockError);
      }
    }

    return order;
  }

  // Update order status
  static async updateOrder(orderData: UpdateOrderData) {
    const { id, ...updateData } = orderData;
    
    const { data, error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update order: ${error.message}`);
    }

    return data;
  }

  // Cancel an order
  static async cancelOrder(id: string) {
    return this.updateOrder({ id, status: 'cancelled' });
  }

  // Get orders by customer
  static async getOrdersByCustomer(customerId: string) {
    return this.getOrders({ customer_id: customerId });
  }

  // Get orders by status
  static async getOrdersByStatus(status: string) {
    return this.getOrders({ status });
  }

  // Get order statistics
  static async getOrderStats() {
    const { data, error } = await supabase
      .from('orders')
      .select('status, total_amount, created_at');

    if (error) {
      throw new Error(`Failed to fetch order stats: ${error.message}`);
    }

    const stats = {
      total_orders: data.length,
      total_revenue: data.reduce((sum, order) => sum + order.total_amount, 0),
      pending_orders: data.filter(order => order.status === 'pending').length,
      processing_orders: data.filter(order => order.status === 'processing').length,
      shipped_orders: data.filter(order => order.status === 'shipped').length,
      delivered_orders: data.filter(order => order.status === 'delivered').length,
      cancelled_orders: data.filter(order => order.status === 'cancelled').length,
    };

    return stats;
  }

  // Get recent orders
  static async getRecentOrders(limit = 10) {
    return this.getOrders({ limit });
  }
}
