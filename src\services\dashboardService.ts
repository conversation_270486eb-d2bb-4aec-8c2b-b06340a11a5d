import { supabase } from '../lib/supabase';
import { OrderService } from './orderService';
import { CustomerService } from './customerService';
import { ProductService } from './productService';

export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
  lowStockProducts: number;
}

export interface SalesData {
  date: string;
  revenue: number;
  orders: number;
}

export interface TopProduct {
  id: string;
  name: string;
  total_sold: number;
  revenue: number;
  image_url: string;
}

export interface RecentActivity {
  id: string;
  type: 'order' | 'customer' | 'product';
  description: string;
  timestamp: string;
  amount?: number;
}

export class DashboardService {
  // Get main dashboard statistics
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get current period stats
      const [orderStats, customerStats, productStats, lowStockProducts] = await Promise.all([
        OrderService.getOrderStats(),
        CustomerService.getCustomerStats(),
        this.getProductCount(),
        ProductService.getLowStockProducts(10)
      ]);

      // Get previous period stats for growth calculation
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const [previousOrderStats, previousCustomerStats] = await Promise.all([
        this.getOrderStatsForPeriod(thirtyDaysAgo),
        this.getCustomerStatsForPeriod(thirtyDaysAgo)
      ]);

      // Calculate growth percentages
      const revenueGrowth = this.calculateGrowth(
        orderStats.total_revenue,
        previousOrderStats.total_revenue
      );
      
      const ordersGrowth = this.calculateGrowth(
        orderStats.total_orders,
        previousOrderStats.total_orders
      );
      
      const customersGrowth = this.calculateGrowth(
        customerStats.total_customers,
        previousCustomerStats.total_customers
      );

      return {
        totalRevenue: orderStats.total_revenue,
        totalOrders: orderStats.total_orders,
        totalCustomers: customerStats.total_customers,
        totalProducts: productStats,
        revenueGrowth,
        ordersGrowth,
        customersGrowth,
        lowStockProducts: lowStockProducts.length
      };
    } catch (error) {
      throw new Error(`Failed to fetch dashboard stats: ${error}`);
    }
  }

  // Get sales data for charts
  static async getSalesData(days = 30): Promise<SalesData[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('orders')
      .select('total_amount, created_at')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch sales data: ${error.message}`);
    }

    // Group by date
    const salesByDate: { [key: string]: { revenue: number; orders: number } } = {};
    
    data.forEach(order => {
      const date = new Date(order.created_at).toISOString().split('T')[0];
      if (!salesByDate[date]) {
        salesByDate[date] = { revenue: 0, orders: 0 };
      }
      salesByDate[date].revenue += order.total_amount;
      salesByDate[date].orders += 1;
    });

    // Convert to array and fill missing dates
    const salesData: SalesData[] = [];
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      const dateStr = date.toISOString().split('T')[0];
      
      salesData.push({
        date: dateStr,
        revenue: salesByDate[dateStr]?.revenue || 0,
        orders: salesByDate[dateStr]?.orders || 0
      });
    }

    return salesData;
  }

  // Get top selling products
  static async getTopProducts(limit = 5): Promise<TopProduct[]> {
    const { data, error } = await supabase
      .from('order_items')
      .select(`
        product_id,
        quantity,
        price,
        products (
          id,
          name,
          image_url
        )
      `);

    if (error) {
      throw new Error(`Failed to fetch top products: ${error.message}`);
    }

    // Group by product and calculate totals
    const productStats: { [key: string]: TopProduct } = {};
    
    data.forEach(item => {
      const productId = item.product_id;
      if (!productStats[productId]) {
        productStats[productId] = {
          id: productId,
          name: item.products.name,
          image_url: item.products.image_url,
          total_sold: 0,
          revenue: 0
        };
      }
      productStats[productId].total_sold += item.quantity;
      productStats[productId].revenue += item.quantity * item.price;
    });

    // Convert to array and sort by revenue
    return Object.values(productStats)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);
  }

  // Get recent activity
  static async getRecentActivity(limit = 10): Promise<RecentActivity[]> {
    const activities: RecentActivity[] = [];

    // Get recent orders
    const recentOrders = await OrderService.getRecentOrders(5);
    recentOrders.forEach(order => {
      activities.push({
        id: `order-${order.id}`,
        type: 'order',
        description: `New order from ${order.customers.first_name} ${order.customers.last_name}`,
        timestamp: order.created_at,
        amount: order.total_amount
      });
    });

    // Get recent customers
    const recentCustomers = await CustomerService.getCustomers({ limit: 5 });
    recentCustomers.forEach(customer => {
      activities.push({
        id: `customer-${customer.id}`,
        type: 'customer',
        description: `New customer: ${customer.first_name} ${customer.last_name}`,
        timestamp: customer.created_at
      });
    });

    // Sort by timestamp and limit
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  // Helper methods
  private static async getProductCount(): Promise<number> {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw new Error(`Failed to count products: ${error.message}`);
    }

    return count || 0;
  }

  private static async getOrderStatsForPeriod(startDate: Date) {
    const { data, error } = await supabase
      .from('orders')
      .select('total_amount')
      .gte('created_at', startDate.toISOString());

    if (error) {
      throw new Error(`Failed to fetch order stats: ${error.message}`);
    }

    return {
      total_orders: data.length,
      total_revenue: data.reduce((sum, order) => sum + order.total_amount, 0)
    };
  }

  private static async getCustomerStatsForPeriod(startDate: Date) {
    const { count, error } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString());

    if (error) {
      throw new Error(`Failed to count customers: ${error.message}`);
    }

    return {
      total_customers: count || 0
    };
  }

  private static calculateGrowth(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}
