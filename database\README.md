# Database Setup Instructions

## 1. Create Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new account if you don't have one
2. Create a new project
3. Wait for the project to be fully set up

## 2. Get Your Credentials

1. In your Supabase project dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public** key (the public anonymous key)

## 3. Update Environment Variables

1. Open the `.env` file in the root of your project
2. Replace the placeholder values with your actual Supabase credentials:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-actual-anon-key-here
```

## 4. Run Database Migrations

1. In your Supabase project dashboard, go to **SQL Editor**
2. Copy and paste the contents of `database/migrations/001_initial_schema.sql`
3. Click **Run** to create all the tables and indexes
4. Copy and paste the contents of `database/migrations/002_sample_data.sql`
5. Click **Run** to insert sample data

## 5. Set Up Row Level Security (Optional but Recommended)

For production, you should enable Row Level Security (RLS) on your tables:

1. In Supabase dashboard, go to **Authentication** > **Policies**
2. Enable RLS on all tables
3. Create appropriate policies for your use case

## 6. Test the Connection

1. Start your development server: `npm run dev`
2. Check the browser console for any connection errors
3. Navigate to the admin panel to see if data loads correctly

## Database Schema

### Tables Created:
- **categories**: Product categories
- **products**: Store products with category relationships
- **customers**: Customer information
- **orders**: Customer orders
- **order_items**: Individual items within orders
- **admin_users**: Admin panel users

### Key Features:
- UUID primary keys for all tables
- Proper foreign key relationships
- Check constraints for data validation
- Automatic timestamps with triggers
- Indexes for performance optimization
- Sample data for testing

## Troubleshooting

### Common Issues:

1. **Connection Error**: Make sure your environment variables are correct
2. **CORS Error**: Ensure your domain is added to the allowed origins in Supabase settings
3. **Permission Error**: Check that RLS policies allow the operations you're trying to perform

### Getting Help:

- Check the Supabase documentation: https://supabase.com/docs
- Review the browser console for detailed error messages
- Verify your API keys and project URL are correct
